const fs = require("fs");
const path = require("path");
const { EmailService, EmailType } = require("./supabase/functions/_shared/email.service.ts");

// Config
const SENDGRID_API_KEY = "*********************************************************************";
const SENDER_EMAIL = "<EMAIL>";

// Example data (replace with your own or accept from CLI/args)
const to = "<EMAIL>";
const subject = "Invoice Failed – Please Review";
const html = "<p>Hello Oleksii, your invoice payment failed. Please check the attachment.</p>";
const files = ["./invoice.pdf"]; // Add more file paths as needed

// Prepare attachments
const attachments = files.map((filePath) => {
  const content = fs.readFileSync(filePath).toString("base64");
  return {
    content,
    filename: path.basename(filePath),
    type: "application/pdf", // You can make this dynamic if needed
    disposition: "attachment",
  };
});

// Create email service
const emailService = new EmailService(SENDGRID_API_KEY, SENDER_EMAIL);

// Send email
emailService
  .sendEmail({
    to,
    from: SENDER_EMAIL,
    emailType: EmailType.SUBSCRIPTION_INVOICE_PAYMENT_FAILED,
    html,
    attachments,
  })
  .then(() => console.log("Email sent"))
  .catch((err) => console.error("Error sending email", err.response?.body || err));