// Test script for create-subcontractor function
// Run with: node test-create-subcontractor.js

const SUPABASE_FUNCTION_URL = 'http://localhost:54321/functions/v1/create-subcontractor';

const testData = {
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  mobile: '+44 7123 456789',
  addressLine1: '123 Main Street',
  addressLine2: 'Apartment 4B',
  postCode: 'SW1A 1AA',
  dateOfBirth: '1985-06-15',
  gasRegistered: true,
  yearsExperience: 8,
  travelDistance: 25,
  hasOwnVan: true,
  hasOwnTools: true,
  workType: 'Plumbing',
  centralLondon: true,
  drivingLicense: true,
  publicLiabilityInsurance: true,
  availableDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
  acceptedRates: true,
  outOfHoursWork: true,
  emergencyCallouts: false,
  preferredWorkType: 'Residential',
  additionalQualifications: 'City & Guilds Level 3'
};

async function testCreateSubcontractor() {
  try {
    console.log('Testing create-subcontractor function...');
    console.log('URL:', SUPABASE_FUNCTION_URL);
    console.log('Test data:', JSON.stringify(testData, null, 2));

    const response = await fetch(SUPABASE_FUNCTION_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData),
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    const result = await response.json();
    console.log('Response body:', JSON.stringify(result, null, 2));

    if (response.ok) {
      console.log('✅ Test passed! Subcontractor created successfully.');
    } else {
      console.log('❌ Test failed! Error response received.');
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

async function testMissingFields() {
  try {
    console.log('\n--- Testing missing required fields ---');
    
    const incompleteData = {
      firstName: 'John',
      // Missing lastName, email, mobile
    };

    const response = await fetch(SUPABASE_FUNCTION_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(incompleteData),
    });

    const result = await response.json();
    console.log('Response status:', response.status);
    console.log('Response body:', JSON.stringify(result, null, 2));

    if (response.status === 400 && result.error) {
      console.log('✅ Validation test passed! Missing fields detected.');
    } else {
      console.log('❌ Validation test failed! Should have returned 400 error.');
    }

  } catch (error) {
    console.error('❌ Validation test failed with error:', error.message);
  }
}

async function testCORS() {
  try {
    console.log('\n--- Testing CORS preflight ---');
    
    const response = await fetch(SUPABASE_FUNCTION_URL, {
      method: 'OPTIONS',
    });

    console.log('CORS Response status:', response.status);
    console.log('CORS headers:', Object.fromEntries(response.headers.entries()));

    if (response.status === 200 && response.headers.get('Access-Control-Allow-Origin')) {
      console.log('✅ CORS test passed!');
    } else {
      console.log('❌ CORS test failed!');
    }

  } catch (error) {
    console.error('❌ CORS test failed with error:', error.message);
  }
}

// Run all tests
async function runAllTests() {
  console.log('🧪 Starting create-subcontractor function tests...\n');
  
  await testCreateSubcontractor();
  await testMissingFields();
  await testCORS();
  
  console.log('\n🏁 All tests completed!');
}

// Check if running directly
if (typeof require !== 'undefined' && require.main === module) {
  runAllTests();
}

module.exports = {
  testCreateSubcontractor,
  testMissingFields,
  testCORS,
  runAllTests
};
