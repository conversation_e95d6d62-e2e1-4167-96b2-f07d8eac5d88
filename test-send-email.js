// Test script for the updated send-email Supabase function
// This demonstrates how to send HTML emails with attachments

const fs = require("fs");
const path = require("path");

// Configuration
const SUPABASE_FUNCTION_URL = "http://localhost:54321/functions/v1/send-email";
// For production, use: https://your-project.supabase.co/functions/v1/send-email

// Example email data
const emailData = {
  to: "<EMAIL>",
  from: "<EMAIL>", // Optional, will use default if not provided
  html: `
    <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; }
          .header { background-color: #f0f0f0; padding: 20px; }
          .content { padding: 20px; }
          .footer { background-color: #e0e0e0; padding: 10px; text-align: center; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>Pleasant Plumbers</h1>
        </div>
        <div class="content">
          <h2>Invoice Payment Failed</h2>
          <p>Hello,</p>
          <p>We wanted to inform you that your recent invoice payment has failed. Please review the attached invoice and update your payment method.</p>
          <p>If you have any questions, please don't hesitate to contact us.</p>
          <p>Best regards,<br>Pleasant Plumbers Team</p>
        </div>
        <div class="footer">
          <p>&copy; 2024 Pleasant Plumbers. All rights reserved.</p>
        </div>
      </body>
    </html>
  `,
  emailType: "SUBSCRIPTION_INVOICE_PAYMENT_FAILED", // Optional, will use default if not provided
  attachments: [] // Will be populated below if files exist
};

// Add attachments if files exist
const attachmentFiles = ["./invoice.pdf", "./receipt.pdf"];

attachmentFiles.forEach((filePath) => {
  if (fs.existsSync(filePath)) {
    const content = fs.readFileSync(filePath).toString("base64");
    const fileExtension = path.extname(filePath).toLowerCase();
    
    // Determine MIME type based on file extension
    let mimeType = "application/octet-stream";
    if (fileExtension === ".pdf") mimeType = "application/pdf";
    else if (fileExtension === ".jpg" || fileExtension === ".jpeg") mimeType = "image/jpeg";
    else if (fileExtension === ".png") mimeType = "image/png";
    else if (fileExtension === ".txt") mimeType = "text/plain";
    
    emailData.attachments.push({
      content,
      filename: path.basename(filePath),
      type: mimeType,
      disposition: "attachment",
    });
    
    console.log(`Added attachment: ${path.basename(filePath)}`);
  } else {
    console.log(`File not found: ${filePath}`);
  }
});

// Function to send email
async function sendEmail() {
  try {
    console.log("Sending email...");
    console.log("To:", emailData.to);
    console.log("Attachments:", emailData.attachments.length);
    
    const response = await fetch(SUPABASE_FUNCTION_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        // Add authorization header if needed
        // "Authorization": `Bearer ${process.env.SUPABASE_ANON_KEY}`
      },
      body: JSON.stringify(emailData),
    });

    const result = await response.json();

    if (response.ok) {
      console.log("✅ Email sent successfully!");
      console.log("Response:", result);
    } else {
      console.error("❌ Failed to send email:");
      console.error("Status:", response.status);
      console.error("Error:", result);
    }
  } catch (error) {
    console.error("❌ Error sending email:", error.message);
  }
}

// Run the test
sendEmail();
