// supabase/functions/create-subcontractor/index.ts

import { SubcontractorService } from "./services/subcontractor.service.ts";
import { createSubcontractorZoh<PERSON> } from "./services/create-subcontractor-zoho.ts";

type SubcontractorRequest = {
  firstName: string;
  lastName: string;
  email: string;
  mobile: string;
  addressLine1: string;
  addressLine2?: string;
  postCode: string;
  dateOfBirth: string;
  gasRegistered: boolean;
  yearsExperience: number;
  travelDistance: number;
  hasOwnVan: boolean;
  hasOwnTools: boolean;
  workType: string;
  centralLondon: boolean;
  drivingLicense: boolean;
  publicLiabilityInsurance: boolean;
  availableDays?: string[];
  acceptedRates: boolean;
  outOfHoursWork: boolean;
  emergencyCallouts: boolean;
  preferredWorkType: string;
  additionalQualifications?: string;
};

async function handleCreateSubcontractor(request: Request): Promise<Response> {
  try {
    // Parse request body
    const body: SubcontractorRequest = await request.json();

    // Validate required fields
    const requiredFields: (keyof SubcontractorRequest)[] = ["firstName", "lastName", "email", "mobile"];
    for (const field of requiredFields) {
      if (!body[field]) {
        return new Response(
          JSON.stringify({
            error: {
              message: `Missing required field: ${field}`,
            },
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }
    }

    // Create subcontractor service instance
    const subcontractorService = new SubcontractorService();

    // Send to Zoho CRM
    const zohoResult = await createSubcontractorZoho(body);
    console.log("Zoho integration result:", zohoResult);

    // Send notification email
    const emailResult = await subcontractorService.sendNotificationEmail({
      to: body.email,
      subcontractorData: body,
    });

    // Check if there was an error in the email service response
    if (emailResult && 'error' in emailResult) {
      console.warn("Email sending failed:", emailResult.error);
      // Continue processing even if email fails
    }

    // Success response
    return new Response(
      JSON.stringify({
        data: {
          message: "Subcontractor created successfully",
          zoho: zohoResult,
          email: emailResult,
        },
      }),
      {
        status: 201,
        headers: { "Content-Type": "application/json" },
      }
    );
  } catch (error) {
    console.error("Error in create-subcontractor function:", error);

    return new Response(
      JSON.stringify({
        error: {
          message: "Internal server error",
          details: error instanceof Error ? error.message : "Unknown error",
        },
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}

// @ts-ignore: Deno is available in Supabase Edge Functions
Deno.serve(async (request: Request) => {
  // Handle CORS preflight requests
  if (request.method === "OPTIONS") {
    return new Response(null, {
      status: 200,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
      },
    });
  }

  // Only allow POST requests
  if (request.method !== "POST") {
    return new Response(
      JSON.stringify({
        error: {
          message: "Method not allowed. Only POST requests are supported.",
        },
      }),
      {
        status: 405,
        headers: { "Content-Type": "application/json" },
      }
    );
  }

  const response = await handleCreateSubcontractor(request);

  // Add CORS headers to the response
  response.headers.set("Access-Control-Allow-Origin", "*");
  response.headers.set("Access-Control-Allow-Methods", "POST, OPTIONS");
  response.headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization");

  return response;
});