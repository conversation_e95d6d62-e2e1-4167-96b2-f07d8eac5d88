"use client";

import Container from "@components/Container";
import Typography from "@components/Typography";
import * as styles from "./SubcontractorBenefits.css";
import React from "react";

const SubcontractorBenefits = () => {
  return (
    <Container>
      <section className={styles.section}>
        <Typography variant="h2" className={styles.title}>
          Our <span className={styles.titleHighlight}>Benefits</span>
        </Typography>
        
        <div className={styles.benefitsContainer}>
          {/* First Benefit Card */}
          <div className={styles.benefitCard.green}>
            <div className={styles.iconWrapper}>
              <div className={styles.icon.star}>★</div>
            </div>
            
            <Typography variant="h3" className={styles.benefitTitle}>
              Consistent,{" "}
              <span className={styles.benefitTitleHighlight}>
                Well-Paid Jobs
              </span>
            </Typography>
            
            <Typography variant="bodyMedium" className={styles.benefitDescription}>
              no time wasted chasing work.
            </Typography>
          </div>
          
          {/* Second Benefit Card */}
          <div className={styles.benefitCard.dark}>
            <div className={styles.iconWrapper}>
              <div className={styles.icon.pound}>£</div>
            </div>
            
            <Typography variant="h3" className={styles.benefitTitle}>
              No Waiting Weeks{" "}
              <span className={styles.benefitTitleHighlight}>
                For Payment
              </span>
            </Typography>
            
            <Typography variant="bodyMedium" className={styles.benefitDescription}>
              get paid the next working day!
            </Typography>
          </div>
        </div>
      </section>
    </Container>
  );
};

export default SubcontractorBenefits;