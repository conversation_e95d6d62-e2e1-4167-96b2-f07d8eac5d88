import { style } from "@vanilla-extract/css";
import { breakpoints, breakpointValues } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import {mode} from "@/styles/functions.css";

export const stepWrapper = style({
  width: "100%",
  display: "flex",
  flexDirection: "column",
});

export const stepHeader = style({
  marginBottom: "30px",
  position: "relative",
  
  "@media": {
    [`screen and (max-width: ${breakpointValues.md - 1}px)`]: {
      marginBottom: "20px",
    }
  }
});

export const stepIndicator = style({
  position: "absolute",
  top: "0",
  right: "0",
  fontSize: "16px",
  color: "#999",
  textAlign: "right",

  "@media": {
    [`screen and (max-width: ${breakpointValues.md - 1}px)`]: {
      position: "absolute",
      top: "0",
      right: "0",
    }
  }
});

export const stepTitle = style({
  fontSize: "28px",
  fontWeight: 600,
  color: theme.colors.primary.castletonGreen,
  marginBottom: "10px",

  "@media": {
    [`screen and (max-width: ${breakpointValues.md - 1}px)`]: {
      fontSize: "24px",
      marginTop: "30px",
    }
  }
});

export const stepDescription = style({
  fontSize: "18px",
  color: theme.colors.primary.castletonGreen,
  marginBottom: "30px",
  maxWidth: "700px",

  "@media": {
    [`screen and (max-width: ${breakpointValues.md - 1}px)`]: {
      fontSize: "16px",
      lineHeight: "1.4",
      marginBottom: "25px",
    }
  }
});

export const form = style({
  display: "flex",
  flexDirection: "column",
  gap: "20px",
  width: "100%",

  "@media": {
    [`screen and (max-width: ${breakpointValues.md - 1}px)`]: {
      gap: "15px",
    }
  }
});

export const formRow = style({
  display: "flex",
  flexDirection: "column",
  gap: "15px",
  width: "100%",

  "@media": {
    [breakpoints.tablet]: {
      flexDirection: "row",
      gap: "20px",
    }
  }
});

export const formGroup = style({
  flex: 1,
  display: "flex",
  flexDirection: "column",
  width: "100%",
  minWidth: 0, // Prevents flex items from overflowing
});

export const label = style({
  fontSize: "16px",
  fontWeight: 500,
  marginBottom: "8px",
  color: theme.colors.primary.castletonGreen,

  "@media": {
    [`screen and (max-width: ${breakpointValues.md - 1}px)`]: {
      marginBottom: "6px",
    }
  }
});

export const input = style({
  padding: "12px 16px",
  borderRadius: "8px",
  border: "1px solid #ddd",
  fontSize: "16px",
  transition: "border-color 0.2s",
  backgroundColor: theme.colors.primary.softWhite,
  width: "100%",

  ":focus": {
    outline: "none",
    borderColor: theme.colors.primary.asidGreen,
  },

  "::placeholder": {
    color: "#aaa",
  },

  "@media": {
    [`screen and (max-width: ${breakpointValues.md - 1}px)`]: {
      padding: "10px 14px",
      fontSize: "14px",
    }
  }
});

export const select = style({
  padding: "12px 16px",
  borderRadius: "8px",
  border: "1px solid #ddd",
  fontSize: "16px",
  backgroundColor: theme.colors.primary.softWhite,
  appearance: "none",
  backgroundImage: "url('data:image/svg+xml;utf8,<svg fill=\"%23888\" height=\"24\" viewBox=\"0 0 24 24\" width=\"24\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M7 10l5 5 5-5z\"/></svg>')",
  backgroundRepeat: "no-repeat",
  backgroundPosition: "right 12px center",
  width: "100%",

  ":focus": {
    outline: "none",
    borderColor: theme.colors.primary.asidGreen,
  },

  "@media": {
    [`screen and (max-width: ${breakpointValues.md - 1}px)`]: {
      padding: "10px 14px",
      fontSize: "14px",
    }
  }
});

export const textarea = style({
  padding: "12px 16px",
  borderRadius: "8px",
  border: "1px solid #ddd",
  fontSize: "16px",
  minHeight: "24px",
  resize: "vertical",
  backgroundColor: theme.colors.primary.softWhite,
  width: "100%",

  ":focus": {
    outline: "none",
    borderColor: theme.colors.primary.asidGreen,
  },

  "::placeholder": {
    color: "#aaa",
  },

  "@media": {
    [`screen and (max-width: ${breakpointValues.md - 1}px)`]: {
      padding: "10px 14px",
      fontSize: "14px",
    }
  }
});

export const checkboxWrapper = style({
  display: "flex",
  alignItems: "flex-start",
  gap: "10px",
});

export const checkbox = style({
  width: "18px",
  height: "18px",
  accentColor: theme.colors.primary.asidGreen,
});

export const checkboxLabel = style({
  fontSize: "16px",
  lineHeight: 1.2,
});

export const radioGroup = style({
  display: "flex",
  gap: "20px",
  flexWrap: "wrap",
});

export const radioOption = style({
  display: "flex",
  alignItems: "center",
  gap: "8px",
  borderRadius: "8px",
  border: "1px solid #DEDED6",
  padding: "14px 20px 14px 18px"
});

export const radioInput = style({
  width: "18px",
  height: "18px",
  accentColor: theme.colors.primary.asidGreen,
});

export const radioLabel = style({
  fontSize: "14px",
});

export const checkboxGroup = style({
  display: "flex",
  flexWrap: "wrap",
  gap: "15px",
});

export const checkboxOption = style({
  display: "flex",
  alignItems: "center",
  gap: "8px",
  border: "1px solid #DEDED6",
  padding: "14px 20px 14px 18px",
  borderRadius: "8px"
});

export const buttonGroup = style({
  display: "flex",
  justifyContent: "space-between",
  marginTop: "30px",
  flexDirection: "column",
  "@media": {
    [breakpoints.tablet]: {
      marginTop: "40px",
      flexDirection: "row",
    }
  }
});

export const referEngineer = style({
  color: theme.colors.primary.asidGreen,
  minWidth: "300px",
})

export const backButton = style({
  minWidth: "120px",
  // marginBottom: "20px",
});

export const nextButton = style({
  minWidth: "180px",
});

export const searchInputWrapper = style({
  position: "relative",
});

export const searchButton = style({
  position: "absolute",
  right: "10px",
  top: "50%",
  transform: "translateY(-50%)",
  background: "none",
  border: "none",
  cursor: "pointer",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
});

export const searchIcon = style({
  fontSize: "18px",
});

export const numberInputWrapper = style({
  position: "relative",
  display: "flex",
  alignItems: "center",
  width: "110px",

  "@media": {
    [breakpoints.tablet]: {
      width: "90%",
    }
  }
});

export const numberInput = style({
  padding: "12px 16px",
  borderRadius: "8px",
  border: "1px solid #ddd",
  fontSize: "16px",
  backgroundColor: theme.colors.primary.softWhite,
  width: "100%",
  appearance: "none",
  textAlign: "left",

  ":focus": {
    outline: "none",
    borderColor: theme.colors.primary.asidGreen,
  },

  "@media": {
    [`screen and (max-width: ${breakpointValues.md - 1}px)`]: {
      padding: "22px 24px",
      fontSize: "18px",
    }
  }
});

export const inputSuffix = style({
  position: "absolute",
  right: "-60px",
  top: "50%",
  transform: "translateY(-50%)",
  color: theme.colors.primary.castletonGreen,
  fontSize: "16px",

  "@media": {
    [`screen and (max-width: ${breakpointValues.md - 1}px)`]: {
      right: "-50px",
      fontSize: "18px",
    }
  }
});

export const numberControls = style({
  position: "absolute",
  right: "10px",
  top: "50%",
  transform: "translateY(-50%)",
  display: "flex",
  flexDirection: "column",
  gap: "2px",
});

export const numberButton = style({
  background: "none",
  border: "none",
  padding: "0",
  cursor: "pointer",
  color: theme.colors.primary.castletonGreen,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  fontSize: "16px",
  width: "20px",
  height: "20px",

  ":hover": {
    color: theme.colors.primary.asidGreen,
  }
});

export const fileUploadGroup = style({
  display: "flex",
  flexDirection: "column",
  gap: "10px",

  "@media": {
    [breakpoints.tablet]: {
      flexDirection: "row",
    }
  }
});

export const fileUploadLabel = style({
  flex: "0 0 30%",

  "@media": {
    [breakpoints.tablet]: {
      paddingRight: "20px",
    }
  }
});

export const fileUploadDescription = style({
  fontSize: "13px",
  color: theme.colors.primary.castletonGreen,
  marginTop: "5px",
});

export const fileUploadControl = style({
  flex: 1,
});

export const fileDropArea = style({
  display: "flex",
  alignItems: "center",
  border: "1px solid #ddd",
  borderRadius: "8px",
  padding: "20px",
  cursor: "pointer",
  transition: "all 0.2s ease",
  backgroundColor: theme.colors.primary.softWhite,
  width: "100%",

  ":hover": {
    borderColor: theme.colors.primary.asidGreen,
  },

  "@media": {
    [`screen and (max-width: ${breakpointValues.md - 1}px)`]: {
      flexDirection: "column",
      alignItems: "center",
      padding: "20px 15px",
      textAlign: "center",
    }
  }
});

export const dragActive = style({
  borderColor: theme.colors.primary.asidGreen,
  backgroundColor: "rgba(0, 128, 0, 0.05)",
});

export const dropText = style({
  fontSize: "16px",
  color: theme.colors.primary.castletonGreen,
});

export const fileSelectButton = style({
  backgroundColor: "transparent",
  border: `1px solid ${theme.colors.primary.castletonGreen}`,
  color: theme.colors.primary.castletonGreen,
  borderRadius: "50px",
  padding: "10px 25px",
  fontSize: "16px",
  cursor: "pointer",
  transition: "all 0.2s ease",
  whiteSpace: "nowrap",

  ":hover": {
    backgroundColor: theme.colors.primary.castletonGreen,
    color: theme.colors.primary.ivory,
  },

  "@media": {
    [`screen and (max-width: ${breakpointValues.md - 1}px)`]: {
      width: "80%",
      padding: "12px 20px",
    }
  }
});

export const hiddenFileInput = style({
  display: "none",
});

export const filePreview = style({
  display: "flex",
  alignItems: "center",
  justifyContent: "space-between",
  width: "100%",
});

export const fileName = style({
  fontSize: "16px",
  color: theme.colors.primary.castletonGreen,
  overflow: "hidden",
  textOverflow: "ellipsis",
  whiteSpace: "nowrap",
  maxWidth: "80%",
});

export const removeFileButton = style({
  backgroundColor: "transparent",
  border: "none",
  color: "#999",
  fontSize: "20px",
  cursor: "pointer",
  padding: "0 5px",

  ":hover": {
    color: "red",
  }
});

export const optionalText = style({
  color: theme.colors.primary.castletonGreen,
  fontWeight: "normal",
  fontSize: "20px",
});

// Confirmation step styles
export const confirmationContent = style({
  textAlign: "left",
  maxWidth: "600px",
  margin: "0 auto",
  padding: "20px 15px",
  color: theme.colors.primary.castletonGreen,
  width: "100%",
});

export const successIcon = style({
  width: "80px",
  height: "80px",
  borderRadius: "50%",
  backgroundColor: theme.colors.primary.asidGreen,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  margin: "0 auto 30px",
  color: theme.colors.primary.softWhite,
});

export const confirmationHeader = style({
  position: "relative",
  display: "flex",
  flexDirection: "column",
  alignItems: "flex-start",
  marginBottom: "20px",
});

export const checkIconWrapper = style({
  display: "inline-flex",
  alignItems: "center",
  justifyContent: "center",
  backgroundColor: "#00E676",
  borderRadius: "50%",
  width: "32px",
  height: "32px",
  marginBottom: "12px",
});

export const checkIcon = style({
  color: "white",
  width: "18px",
  height: "18px",
});

export const confirmationTitle = style({
  fontSize: "28px",
  fontWeight: 600,
  color: theme.colors.primary.castletonGreen,
  marginBottom: "8px",
});

export const confirmationMessage = style({
  fontSize: "16px",
  lineHeight: 1.5,
  color: theme.colors.primary.castletonGreen,
  marginBottom: "32px",
});

export const whatHappensNext = style({
  marginBottom: "32px",
});

export const whatHappensNextTitle = style({
  fontSize: "18px",
  fontWeight: 600,
  color: theme.colors.primary.castletonGreen,
  marginBottom: "12px",
});

export const nextStepDescription = style({
  fontSize: "16px",
  lineHeight: 1.5,
  color: theme.colors.primary.castletonGreen,
  marginBottom: "24px",
});

export const welcomePackTitle = style({
  fontSize: "16px",
  fontWeight: 600,
  color: theme.colors.primary.castletonGreen,
  marginBottom: "12px",
});

export const welcomePackList = style({
  listStyle: "none",
  padding: 0,
  margin: "0 0 24px 0",
});

export const welcomePackItem = style({
  fontSize: "16px",
  lineHeight: 1.5,
  color: theme.colors.primary.castletonGreen,
  marginBottom: "8px",
});

export const systemsMessage = style({
  fontSize: "16px",
  lineHeight: 1.5,
  color: theme.colors.primary.castletonGreen,
});

export const contactSection = style({
  marginBottom: "32px",
});

export const contactTitle = style({
  fontSize: "18px",
  fontWeight: 600,
  color: theme.colors.primary.castletonGreen,
  marginBottom: "12px",
});

export const contactInfo = style({
  fontSize: "16px",
  lineHeight: 1.5,
  color: theme.colors.primary.castletonGreen,
});

export const emailLink = style({
  color: theme.colors.primary.castletonGreen,
  textDecoration: "none",
  fontWeight: 700,

  ":hover": {
    textDecoration: "underline",
  }
});

export const confirmationButtons = style({
  display: "flex",
  gap: "16px",
  marginBottom: "24px",
  marginTop: "24px",
  width: "100%",
  flexWrap: "wrap",

  "@media": {
    [breakpoints.tablet]: {
      flexWrap: "nowrap",
    },
    [`screen and (max-width: 500px)`]: {
      flexDirection: "column",
      gap: "12px",
    }
  }
});

export const homeButton = style({
  backgroundColor: theme.colors.primary.asidGreen,
  color: "white",
  flex: 1,
  minWidth: "120px",

  ":hover": {
    backgroundColor: "#00C853",
  }
});

export const questionsSection = style({
  marginTop: "16px",
  width: "100%",
});

export const questionsTitle = style({
  fontSize: "18px",
  fontWeight: 600,
  color: theme.colors.primary.castletonGreen,
  marginBottom: "16px",
});

export const contactButtons = style({
  display: "flex",
  gap: "16px",
  width: "100%",

  "@media": {
    [`screen and (max-width: 500px)`]: {
      flexDirection: "column",
      gap: "12px",
    }
  }
});

export const whatsappButton = style({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  gap: "8px",
  flex: 1,
  minWidth: "120px",
});

export const callButton = style({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  gap: "8px",
  flex: 1,
  minWidth: "120px",
});

export const buttonIcon = style({
  width: "20px",
  height: "20px",
});

// Document upload step styles
export const documentSection = style({
  marginBottom: "40px",

  "@media": {
    [`screen and (max-width: ${breakpointValues.md - 1}px)`]: {
      marginBottom: "30px",
    }
  }
});

export const documentLabel = style({
  fontSize: "20px",
  fontWeight: "normal",
  marginBottom: "8px",
  color: theme.colors.primary.castletonGreen,
});

export const documentDescription = style({
  fontSize: "16px",
  color: theme.colors.primary.castletonGreen,
  marginBottom: "20px",

  "@media": {
    [`screen and (max-width: ${breakpointValues.md - 1}px)`]: {
      fontSize: "14px",
      lineHeight: "1.4",
      marginBottom: "15px",
    }
  }
});

export const subSection = style({
  marginBottom: "20px",
});

export const subSectionLabel = style({
  fontSize: "18px",
  fontWeight: 600,
  color: theme.colors.primary.castletonGreen,
  marginBottom: "10px",
});

export const uploadContainer = style({
  display: "flex",
  alignItems: "center",
  width: "100%",
  justifyContent: "center",
  flexDirection: "column",

  "@media": {
    [breakpoints.tablet]: {
      alignItems: "center",
      flexDirection: "row"
    }
  }
  // "@media": {
  //   [`screen and (max-width: ${breakpointValues.md - 1}px)`]: {
  //     flexDirection: "column",
  //     justifyContent: "space-between",
  //   }
  // }
});

export const dropTextContainer = style({
  display: "flex",
  flexDirection: "column",
  gap: "8px",

  "@media": {
    [breakpoints.tablet]: {
      flexDirection: "row",
    }
  }
});

export const orText = style({
  fontSize: "16px",
  color: "#999",
  margin: "0 15px",

  "@media": {
    [`screen and (max-width: ${breakpointValues.md - 1}px)`]: {
      margin: "0 0 5px 0",
    }
  }
});

export const sectionTitle = style({
  fontSize: "16px",
  fontWeight: 600,
  color: theme.colors.primary.castletonGreen,
  marginTop: "10px",
  marginBottom: "8px",
});

export const sectionDescription = style({
  fontSize: "14px",
  color: "#666",
  marginBottom: "15px",
});

export const errorText = style({
  color: "#ef4444",
  fontSize: "14px",
  marginTop: "4px",
});

export const required = style({
  color: "#ef4444",
  marginLeft: "2px",
});

export const labelText = style({
  fontWeight: "bold",
});
