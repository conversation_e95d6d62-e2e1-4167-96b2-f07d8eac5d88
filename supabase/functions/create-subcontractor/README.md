# Create Subcontractor Function

This Supabase Edge Function handles subcontractor registration by creating records in Zoho CRM and sending notification emails.

## Features

- ✅ Create subcontractor records in Zoho CRM (mock implementation)
- ✅ Send welcome/notification emails to subcontractors
- ✅ Data validation and error handling
- ✅ Modular architecture with separated business logic
- ✅ CORS support
- ✅ Comprehensive logging

## Environment Variables

Make sure to set these environment variables in your Supabase project:

```
SENDGRID_API_KEY=your_sendgrid_api_key
SENDGRID_SENDER_EMAIL=<EMAIL>
```

## API Endpoint

**POST** `/functions/v1/create-subcontractor`

## Request Body

```typescript
{
  firstName: string;                    // Required: First name
  lastName: string;                     // Required: Last name
  email: string;                        // Required: Email address
  mobile: string;                       // Required: Mobile number
  addressLine1: string;                 // Required: Address line 1
  addressLine2?: string;                // Optional: Address line 2
  postCode: string;                     // Required: Post code
  dateOfBirth: string;                  // Required: Date of birth
  gasRegistered: boolean;               // Required: Gas registration status
  yearsExperience: number;              // Required: Years of experience
  travelDistance: number;               // Required: Travel distance in miles
  hasOwnVan: boolean;                   // Required: Has own van
  hasOwnTools: boolean;                 // Required: Has own tools
  workType: string;                     // Required: Type of work
  centralLondon: boolean;               // Required: Works in central London
  drivingLicense: boolean;              // Required: Has driving license
  publicLiabilityInsurance: boolean;    // Required: Has public liability insurance
  availableDays?: string[];             // Optional: Available working days
  acceptedRates: boolean;               // Required: Accepted rates
  outOfHoursWork: boolean;              // Required: Available for out of hours work
  emergencyCallouts: boolean;           // Required: Available for emergency callouts
  preferredWorkType: string;            // Required: Preferred work type
  additionalQualifications?: string;    // Optional: Additional qualifications
}
```

## Usage Examples

### Create Subcontractor

```javascript
const response = await fetch('/functions/v1/create-subcontractor', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    mobile: '+44 7123 456789',
    addressLine1: '123 Main Street',
    addressLine2: 'Apartment 4B',
    postCode: 'SW1A 1AA',
    dateOfBirth: '1985-06-15',
    gasRegistered: true,
    yearsExperience: 8,
    travelDistance: 25,
    hasOwnVan: true,
    hasOwnTools: true,
    workType: 'Plumbing',
    centralLondon: true,
    drivingLicense: true,
    publicLiabilityInsurance: true,
    availableDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
    acceptedRates: true,
    outOfHoursWork: true,
    emergencyCallouts: false,
    preferredWorkType: 'Residential',
    additionalQualifications: 'City & Guilds Level 3'
  }),
});
```

## Response Format

### Success Response

```json
{
  "data": {
    "message": "Subcontractor created successfully",
    "zoho": {
      "success": true,
      "recordId": "ZOHO_1234567890_abc123",
      "data": { /* Zoho CRM response */ }
    },
    "email": { /* Email service response */ }
  }
}
```

### Error Response

```json
{
  "error": {
    "message": "Error description",
    "details": "Additional error information"
  }
}
```

## Architecture

The function is organized into separate modules:

- **`index.ts`**: Main handler with request/response logic
- **`services/subcontractor.service.ts`**: Business logic for email notifications and data validation
- **`services/create-subcontractor-zoho.ts`**: Mock Zoho CRM integration service

## Services

### SubcontractorService

Handles email notifications and data validation:
- `sendNotificationEmail()`: Sends welcome email to subcontractor
- `validateSubcontractorData()`: Validates input data
- `generateWelcomeEmailHtml()`: Creates HTML email content

### Zoho Integration (Mock)

Mock functions for Zoho CRM integration:
- `createSubcontractorZoho()`: Creates subcontractor record in Zoho
- `updateSubcontractorStatusZoho()`: Updates record status
- `searchSubcontractorZoho()`: Searches for existing records

## Testing

The function can be tested locally using Supabase CLI:

```bash
supabase functions serve create-subcontractor
```

## Notes

- The Zoho integration is currently a mock implementation
- Email notifications are sent automatically upon successful registration
- The function includes comprehensive error handling and logging
- CORS headers are included for browser requests
- Data validation ensures required fields are present
