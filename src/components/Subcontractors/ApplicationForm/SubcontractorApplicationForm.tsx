"use client";

import React, { useState, useEffect } from "react";
import * as styles from "./SubcontractorApplicationForm.css";
import PersonalInfoStep from "./Steps/PersonalInfoStep";
import WorkPreferencesStep from "./Steps/WorkPreferencesStep";
import DocumentUploadStep from "./Steps/DocumentUploadStep";
import ConfirmationStep from "./Steps/ConfirmationStep";
import FormSidebar from "./FormSidebar/FormSidebar";
import { useRouter } from "next/navigation";
import { createClient } from "@supabase/supabase-js";

const STORAGE_KEY = "subcontractor_application_form_data";

export type FormData = {
  // Personal Information
  firstName: string;
  lastName: string;
  mobile: string;
  email: string;
  addressLine1: string;
  addressLine2: string;
  postCode: string;
  dateOfBirth: string;
  hearAboutUs: string;
  contactConsent: boolean;
  referred?: string;
  referrerName?: string;

  // Work Preferences
  gasRegistered: boolean | undefined;
  yearsExperience: number;
  travelDistance: number;
  hasOwnVan: boolean | undefined;
  hasOwnTools: boolean | undefined;
  workType: "Domestic" | "Commercial" | "Both" | undefined;
  centralLondon: boolean | undefined;
  drivingLicense: boolean | undefined;
  publicLiabilityInsurance: boolean | undefined;
  availableDays: string[];
  acceptedRates: boolean | undefined;
  outOfHoursWork: boolean | undefined;
  emergencyCallouts: boolean | undefined;
  preferredWorkType: "Reactive" | "Maintenance" | "Installations" | undefined;
  additionalQualifications: string;

  // Documents
  idPhoto: File | null;
  vanFrontPhoto: File | null;
  vanBackPhoto: File | null;
  vanLeftPhoto: File | null;
  vanRightPhoto: File | null;
  gasSafeCard: File | null;
  insuranceProof: File | null;
  additionalDocuments: File | null;
};

const SubcontractorApplicationForm: React.FC = () => {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState<number>(1);
  const [formData, setFormData] = useState<FormData>(() => {
    // Try to load saved data from localStorage on initial render
    if (typeof window !== 'undefined') {
      const savedData = localStorage.getItem(STORAGE_KEY);
      if (savedData) {
        try {
          const parsedData = JSON.parse(savedData);
          // Convert File objects back to null since they can't be stored in localStorage
          return {
            ...parsedData,
            idPhoto: null,
            vanFrontPhoto: null,
            vanBackPhoto: null,
            vanLeftPhoto: null,
            vanRightPhoto: null,
            gasSafeCard: null,
            insuranceProof: null,
            additionalDocuments: null,
          };
        } catch (e) {
          console.error("Error parsing saved form data:", e);
        }
      }
    }

    return {
      firstName: "",
      lastName: "",
      mobile: "",
      email: "",
      addressLine1: "",
      addressLine2: "",
      postCode: "",
      dateOfBirth: "",
      hearAboutUs: "",
      contactConsent: false,

      gasRegistered: undefined,
      yearsExperience: 0,
      travelDistance: 0,
      hasOwnVan: undefined,
      hasOwnTools: undefined,
      workType: undefined,
      centralLondon: undefined,
      drivingLicense: undefined,
      publicLiabilityInsurance: undefined,
      availableDays: [],
      acceptedRates: undefined,
      outOfHoursWork: undefined,
      emergencyCallouts: undefined,
      preferredWorkType: undefined,
      additionalQualifications: "",

      // Documents
      idPhoto: null,
      vanFrontPhoto: null,
      vanBackPhoto: null,
      vanLeftPhoto: null,
      vanRightPhoto: null,
      gasSafeCard: null,
      insuranceProof: null,
      additionalDocuments: null,
    };
  });

  // Save form data to localStorage whenever it changes
  useEffect(() => {
    // Create a copy without File objects (they can't be serialized)
    const dataForStorage = {
      ...formData,
      idPhoto: null,
      vanFrontPhoto: null,
      vanBackPhoto: null,
      vanLeftPhoto: null,
      vanRightPhoto: null,
      gasSafeCard: null,
      insuranceProof: null,
      additionalDocuments: null,
    };
    
    localStorage.setItem(STORAGE_KEY, JSON.stringify(dataForStorage));
  }, [formData]);

  const handleNext = () => {
    setCurrentStep(prev => Math.min(prev + 1, 4));
  };

  const handleBack = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async () => {
    try {
      // Format the payload
      const payload = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        mobile: formData.mobile,
        addressLine1: formData.addressLine1,
        addressLine2: formData.addressLine2,
        postCode: formData.postCode,
        dateOfBirth: formData.dateOfBirth,
        hearAboutUs: formData.hearAboutUs,
        
        // Work preferences
        gasRegistered: formData.gasRegistered,
        yearsExperience: formData.yearsExperience,
        travelDistance: formData.travelDistance,
        hasOwnVan: formData.hasOwnVan,
        hasOwnTools: formData.hasOwnTools,
        workType: formData.workType,
        centralLondon: formData.centralLondon,
        drivingLicense: formData.drivingLicense,
        publicLiabilityInsurance: formData.publicLiabilityInsurance,
        availableDays: formData.availableDays,
        acceptedRates: formData.acceptedRates,
        outOfHoursWork: formData.outOfHoursWork,
        emergencyCallouts: formData.emergencyCallouts,
        preferredWorkType: formData.preferredWorkType,
        additionalQualifications: formData.additionalQualifications,
      };

      console.log("Submitting application:", payload);

      // Uncomment this when ready to submit to backend
      // const supabase = createClient(
      //   process.env.NEXT_PUBLIC_SUPABASE_URL!,
      //   process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      // );
      // const response = await supabase.functions.invoke("send-leads-to-zoho", {
      //   method: "POST",
      //   body: payload,
      // });
      //
      // if (response.error) {
      //   throw new Error(response.error.message);
      // }

      // Clear saved form data after successful submission
      localStorage.removeItem(STORAGE_KEY);
      
      // Move to confirmation step
      setCurrentStep(4);
    } catch (error) {
      console.error("Error submitting application:", error);
      // You could add error handling UI here
    }
  };

  const updateFormData = (data: Partial<FormData>) => {
    setFormData(prev => ({ ...prev, ...data }));
  };

  return (
    <div className={styles.formContainer}>
      <div className={styles.formWrapper}>
        <div className={styles.formContent}>
          <FormSidebar currentStep={currentStep} />

          <div className={styles.stepContainer}>
            {currentStep === 1 && (
              <PersonalInfoStep
                formData={formData}
                updateFormData={updateFormData}
                onNext={handleNext}
              />
            )}

            {currentStep === 2 && (
              <WorkPreferencesStep
                formData={formData}
                updateFormData={updateFormData}
                onNext={handleNext}
                onBack={handleBack}
              />
            )}

            {currentStep === 3 && (
              <DocumentUploadStep
                formData={formData}
                updateFormData={updateFormData}
                onSubmit={handleSubmit}
                onBack={handleBack}
              />
            )}

            {currentStep === 4 && (
              <ConfirmationStep
                onBackToHome={() => {}}
                onBack={handleBack}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubcontractorApplicationForm;
