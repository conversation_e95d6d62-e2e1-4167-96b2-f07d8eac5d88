import React from "react";
import * as styles from "./Steps.css";
import Button from "@/components/Button";
import Link from "next/link";
import Image from "next/image";
import vector from "@components/ProtectYourBusiness/components/FeatureList/vector.png";
import { theme } from "@/styles/themes.css";
import {referEngineer} from "./Steps.css";
// import checkImg from "../../"

interface ConfirmationStepProps {
  onBackToHome: () => void;
  onBack: () => void;
}

const ConfirmationStep: React.FC<ConfirmationStepProps> = ({
  onBackToHome,
  onBack
}) => {
  return (
    <div className={styles.stepWrapper}>
      <div className={styles.confirmationContent}>
        <h2 className={styles.confirmationTitle}>
          <Image src={vector.src} alt="" width={16} height={16} className={styles.checkIcon} /> You&apos;re All Set!
        </h2>
        <div className={styles.stepIndicator}>Step 4/4</div>
        
        <p className={styles.confirmationMessage}>
          Thanks for submitting your application. We&apos;re excited to welcome you to the Pleasant Plumbers network.
        </p>

        <div className={styles.whatHappensNext}>
          <h3 className={styles.whatHappensNextTitle}>What Happens Next:</h3>
          <p className={styles.nextStepDescription}>
            One of our team members will give you a call within 1-2 working days for a quick introduction
          </p>

          <h3 className={styles.welcomePackTitle}>You&apos;ll receive your Welcome Pack including:</h3>
          <ul className={styles.welcomePackList}>
            <li className={styles.welcomePackItem}>• Branded Uniform</li>
            <li className={styles.welcomePackItem}>• Custom Tool Mat</li>
            <li className={styles.welcomePackItem}>• Pleasant Plumbers ID Card</li>
          </ul>

          <p className={styles.systemsMessage}>
            You&apos;ll also be added to our internal systems and job allocation tools so we can start offering you
            work that fits your availability and skills.
          </p>
        </div>

        <div className={styles.contactSection}>
          <h3 className={styles.contactTitle}>Need anything in the meantime?</h3>
          <p className={styles.contactInfo}>
            Call us or WhatsApp us on 0800 046 1000 or email
          </p>
          <a href="mailto:<EMAIL>" className={styles.emailLink}><EMAIL></a>
        </div>

        <div className={styles.confirmationButtons}>
          <Button
            variant="outlined"
            color="secondary"
            onClick={onBack}
            className={styles.backButton}
          >
            ← Back
          </Button>

          <Button
              variant="outlined"
              color="secondary"
              onClick={onBack}
              as={Link}
              href="/"
              className={styles.referEngineer}
              style={{
                backgroundColor: theme.colors.primary.castletonGreen,
                color: theme.colors.primary.softWhite,
                minWidth: 250,
              }}
          >
            Refer an Engineer
          </Button>

          <Button
            variant="outlined"
            as={Link}
            href="/"
            className={styles.homeButton}
            style={{
              backgroundColor: theme.colors.primary.softWhite,
              color: theme.colors.primary.castletonGreen,
              border: `1px solid ${theme.colors.primary.castletonGreen}`,
            }}
          >
            To Home →
          </Button>
        </div>

        <div className={styles.questionsSection}>
          <h3 className={styles.questionsTitle}>Got any questions?</h3>
          <div className={styles.contactButtons}>
            <Button
              variant="outlined"
              color="secondary"
              className={styles.whatsappButton}
              style={{ 
              backgroundColor: theme.colors.primary.castletonGreen, 
              color: theme.colors.primary.softWhite 
              }}
              onClick={() => window.open('https://wa.me/448000461000', '_blank')}
            >
              WhatsApp Us
            </Button>

            <Button
              variant="outlined"
              color="secondary"
              className={styles.callButton}
              onClick={() => window.open('tel:08000461000', '_blank')}
            >
              Call Us
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConfirmationStep;
