import { style } from "@vanilla-extract/css";
import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";

export const sidebar = style({
  backgroundColor: theme.colors.primary.castletonGreen,
  color: "white",
  padding: "30px 20px",
  display: "flex",
  flexDirection: "column",
  borderRadius: "24px",
  marginBottom: "20px",

  "@media": {
    [breakpoints.tablet]: {
      // width: "690px",
      maxHeight: "860px",
      marginRight: "20px",
      marginBottom: 0,
    },
    [breakpoints.desktop]: {
      width: "690px",
    }
  }
});

export const logoContainer = style({
  marginBottom: "30px",
  marginLeft: "auto",
  marginRight: "auto"
});

export const logo = style({
  filter: "brightness(0) invert(1)",
  width: "98px",
  height: "44",
});

export const content = style({
  display: "flex",
  flexDirection: "column",
  flex: 1,
});

export const heading = style({
  fontFamily: theme.fonts.primary,
  fontStyle: "normal",
  fontWeight: 400,
  lineHeight: "95%",
  letterSpacing: "-1.28px",
  fontSize: "42px",
  marginBottom: "32px",
  textAlign: "center",

  "@media": {
    [breakpoints.tablet]: {
      fontSize: "64px",
      marginBottom: "40px",
    },
  }
});

export const italicText = style({
  fontStyle: "italic"
})

export const texBold = style({
  fontWeight: 600
})

export const regularText = style({
  color: theme.colors.primary.ivory,
});

export const highlightText = style({
  color: theme.colors.primary.asidGreen,
});

export const imageContainer = style({
  flex: 1,
  display: "flex",
  alignItems: "flex-end",
  justifyContent: "center",
  maxHeight: "450px",
});

export const plumberImage = style({
  // maxWidth: "275px",
  height: "275px",
  position: "relative",
  top: "29px",
  backgroundSize: "cover",
  backgroundPosition: "center top",

  "@media": {
    [breakpoints.mobile]: {
      width: "329px",
      height: "337px",
      borderRadius: "24px",
    },
    [breakpoints.tablet]: {
      top: "17px",
      height: "450px",
      // maxWidth: "460px",
      width: "460px",
    }
  }
});
